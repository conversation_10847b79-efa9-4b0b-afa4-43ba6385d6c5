{"name": "verificar-disponibilidade-simples", "nodes": [{"parameters": {"httpMethod": "POST", "path": "verificar-disponibilidade", "responseMode": "responseNode", "options": {}}, "id": "webhook-verificar", "name": "Webhook - Verificar Disponibilidade", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [360, -40], "webhookId": "verificar-disponibilidade"}, {"parameters": {"jsCode": "// Simular verificação de disponibilidade\nconst webhookData = $input.first().json.body;\nconst data = webhookData.data;\nconst horario = webhookData.horario;\n\n// Simular horários ocupados (para teste)\nconst horariosOcupados = {\n  '2024-12-20': ['10:00', '15:00'],\n  '2024-12-21': ['09:00', '16:00']\n};\n\n// Verificar se o horário está ocupado\nconst ocupadosHoje = horariosOcupados[data] || [];\nconst disponivel = !ocupadosHoje.includes(horario);\n\nif (disponivel) {\n  return {\n    json: {\n      disponivel: true,\n      message: \"Hor<PERSON>rio disponível para agendamento\",\n      dataHora: webhookData.dataHoraInicio\n    }\n  };\n} else {\n  // Gerar horários alternativos\n  const todosHorarios = ['08:00', '09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00'];\n  const horariosLivres = todosHorarios.filter(h => !ocupadosHoje.includes(h) && h !== horario);\n  \n  const horariosFormatados = horariosLivres.map(horario => {\n    const hora = parseInt(horario.split(':')[0]);\n    const horaFim = hora + 1;\n    return {\n      horario: horario,\n      dataHoraInicio: `${data}T${horario}:00-03:00`,\n      dataHoraFim: `${data}T${String(horaFim).padStart(2, '0')}:00:00-03:00`\n    };\n  });\n  \n  return {\n    json: {\n      disponivel: false,\n      message: \"Horário não disponível. Veja os horários alternativos:\",\n      dataOriginal: data,\n      horarioSolicitado: horario,\n      horariosAlternativos: horariosFormatados\n    }\n  };\n}"}, "id": "verificar-logica", "name": "Verificar Lógica", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [580, -40]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}", "options": {"responseCode": 200}}, "id": "resposta-final", "name": "Resposta Final", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [800, -40]}], "connections": {"Webhook - Verificar Disponibilidade": {"main": [[{"node": "Verificar Lógica", "type": "main", "index": 0}]]}, "Verificar Lógica": {"main": [[{"node": "Resposta Final", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}}