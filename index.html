<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agendamento de Visita - Imobiliária</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .success-message, .error-message {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
            text-align: center;
            font-weight: 600;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .required {
            color: #e74c3c;
        }

        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Agendar Visita</h1>
            <p>Preencha os dados para agendar sua visita ao imóvel</p>
        </div>

        <form id="agendamentoForm">
            <div class="form-group">
                <label for="nome">Nome Completo <span class="required">*</span></label>
                <input type="text" id="nome" name="nome" required placeholder="Digite seu nome completo">
            </div>

            <div class="form-group">
                <label for="whatsapp">WhatsApp <span class="required">*</span></label>
                <input type="tel" id="whatsapp" name="whatsapp" required placeholder="(11) 99999-9999">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="data">Data da Visita <span class="required">*</span></label>
                    <input type="date" id="data" name="data" required>
                </div>
                <div class="form-group">
                    <label for="horario">Horário <span class="required">*</span></label>
                    <select id="horario" name="horario" required>
                        <option value="">Selecione</option>
                        <option value="08:00">08:00</option>
                        <option value="09:00">09:00</option>
                        <option value="10:00">10:00</option>
                        <option value="11:00">11:00</option>
                        <option value="14:00">14:00</option>
                        <option value="15:00">15:00</option>
                        <option value="16:00">16:00</option>
                        <option value="17:00">17:00</option>
                        <option value="18:00">18:00</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="imovel">Imóvel de Interesse <span class="required">*</span></label>
                <select id="imovel" name="imovel" required>
                    <option value="">Selecione o imóvel</option>
                    <option value="Apartamento 2 quartos - Centro - Ref: AP001">Apartamento 2 quartos - Centro - Ref: AP001</option>
                    <option value="Casa 3 quartos - Jardim América - Ref: CA002">Casa 3 quartos - Jardim América - Ref: CA002</option>
                    <option value="Apartamento 3 quartos - Vila Madalena - Ref: AP003">Apartamento 3 quartos - Vila Madalena - Ref: AP003</option>
                    <option value="Casa 4 quartos - Alphaville - Ref: CA004">Casa 4 quartos - Alphaville - Ref: CA004</option>
                    <option value="Studio - Bela Vista - Ref: ST005">Studio - Bela Vista - Ref: ST005</option>
                </select>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                📅 Agendar Visita
            </button>

            <div class="loading" id="loading">
                <p>⏳ Processando seu agendamento...</p>
            </div>

            <div class="success-message" id="successMessage">
                ✅ Agendamento realizado com sucesso! Você receberá uma confirmação no WhatsApp.
            </div>

            <div class="error-message" id="errorMessage">
                ❌ Erro ao realizar agendamento. Tente novamente ou entre em contato conosco.
            </div>
        </form>
    </div>

    <script>
        /*
        FORMATO DOS DADOS ENVIADOS PARA O N8N:
        {
            "nome": "João Silva",
            "whatsapp": "5511999999999",
            "data": "2024-01-15",
            "horario": "14:00",
            "imovel": "Apartamento 2 quartos - Centro - Ref: AP001",
            "dataHoraInicio": "2024-01-15T14:00:00-03:00",  // Formato ISO 8601 para Google Calendar
            "dataHoraFim": "2024-01-15T15:00:00-03:00",     // 1 hora depois do início
            "dataOriginal": "2024-01-15",                   // Campos originais mantidos
            "horarioOriginal": "14:00"                      // para compatibilidade
        }
        */

        // Configurar data mínima (hoje)
        document.getElementById('data').min = new Date().toISOString().split('T')[0];

        // Mascarar WhatsApp
        document.getElementById('whatsapp').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');

            if (value.length >= 11) {
                value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 7) {
                value = value.replace(/(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{2})(\d{0,5})/, '($1) $2');
            }

            e.target.value = value;
        });

        // Enviar formulário
        document.getElementById('agendamentoForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');

            // Resetar mensagens
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';

            // Mostrar loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'Enviando...';
            loading.style.display = 'block';

            // Coletar dados do formulário
            const formData = new FormData(e.target);
            const data = {};

            for (let [key, value] of formData.entries()) {
                if (key === 'whatsapp') {
                    // Limpar formatação do WhatsApp
                    data[key] = '55' + value.replace(/\D/g, '');
                } else {
                    data[key] = value;
                }
            }

            // Combinar data e horário para formato ISO 8601 (Google Calendar)
            if (data.data && data.horario) {
                // Criar data/hora de início no formato ISO 8601 com timezone do Brasil
                const dataHoraInicio = `${data.data}T${data.horario}:00-03:00`;

                // Calcular data/hora de fim (1 hora depois)
                const inicioDate = new Date(dataHoraInicio);
                const fimDate = new Date(inicioDate.getTime() + 60 * 60 * 1000); // +1 hora
                const dataHoraFim = fimDate.toISOString().replace('Z', '-03:00');

                // Adicionar campos formatados para o Google Calendar
                data.dataHoraInicio = dataHoraInicio;
                data.dataHoraFim = dataHoraFim;

                // Manter campos originais para compatibilidade
                data.dataOriginal = data.data;
                data.horarioOriginal = data.horario;
            }

            try {
                // SUBSTITUA ESTA URL PELA URL DO SEU WEBHOOK N8N
                const webhookUrl = 'https://n8n.algoritmobr.com.br/webhook/processar';

                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    successMessage.style.display = 'block';
                    e.target.reset();
                    // Reagendar data mínima
                    document.getElementById('data').min = new Date().toISOString().split('T')[0];
                } else {
                    throw new Error('Erro na requisição');
                }

            } catch (error) {
                console.error('Erro:', error);
                errorMessage.style.display = 'block';
            } finally {
                // Resetar botão
                submitBtn.disabled = false;
                submitBtn.textContent = '📅 Agendar Visita';
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html>