{"name": "verificar-disponibilidade", "nodes": [{"parameters": {"httpMethod": "POST", "path": "verificar-disponibilidade", "responseMode": "responseNode", "options": {}}, "id": "webhook-verificar", "name": "Webhook - Verificar Disponibilidade", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [360, -40], "webhookId": "verificar-disponibilidade"}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "Visitas"}, "timeMin": "={{ $json.body.dataHoraInicio }}", "timeMax": "={{ $json.body.dataHoraFim }}", "options": {}}, "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1.3, "position": [580, -40], "id": "verificar-calendario", "name": "Verificar Calendario", "credentials": {"googleCalendarOAuth2Api": {"id": "6BP3Rlb3TQDwnD6e", "name": "Google Calendar account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "disponibilidade-check", "leftValue": "={{ $input.all().length }}", "rightValue": "0", "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [800, -40], "id": "check-disponibilidade", "name": "Check Disponibilidade"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"disponivel\": true,\n  \"message\": \"Hor<PERSON>rio disponível para agendamento\",\n  \"dataHora\": \"{{ $('Webhook - Verificar Disponibilidade').item.json.body.dataHoraInicio }}\"\n}", "options": {"responseCode": 200}}, "id": "resposta-disponivel", "name": "Resposta - Disponível", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1020, -140]}, {"parameters": {"jsCode": "// Gerar horários alternativos para o mesmo dia\nconst webhookData = $('Webhook - Verificar Disponibilidade').first().json.body;\nconst dataOriginal = webhookData.data;\nconst horarioSolicitado = webhookData.horario;\n\n// Lista de horários disponíveis para agendamento\nconst todosHorarios = ['08:00', '09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00'];\n\n// Filtrar horário solicitado das alternativas\nconst horariosAlternativos = todosHorarios.filter(h => h !== horarioSolicitado);\n\n// Formatar horários para o Google Calendar\nconst horariosFormatados = horariosAlternativos.map(horario => {\n  const hora = parseInt(horario.split(':')[0]);\n  const horaFim = hora + 1;\n  return {\n    horario: horario,\n    dataHoraInicio: `${dataOriginal}T${horario}:00-03:00`,\n    dataHoraFim: `${dataOriginal}T${String(horaFim).padStart(2, '0')}:00:00-03:00`\n  };\n});\n\nreturn {\n  json: {\n    disponivel: false,\n    message: \"Horário não disponível. Veja os horários alternativos:\",\n    dataOriginal: dataOriginal,\n    horarioSolicitado: horarioSolicitado,\n    horariosAlternativos: horariosFormatados\n  }\n};"}, "id": "gerar-alternativas", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1020, 60]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}", "options": {"responseCode": 200}}, "id": "resposta-indisponivel", "name": "Resposta - Indisponível", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1240, 60]}], "connections": {"Webhook - Verificar Disponibilidade": {"main": [[{"node": "Verificar Calendario", "type": "main", "index": 0}]]}, "Verificar Calendario": {"main": [[{"node": "Check Disponibilidade", "type": "main", "index": 0}]]}, "Check Disponibilidade": {"main": [[{"node": "Resposta - Disponível", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Gerar Alternativas": {"main": [[{"node": "Resposta - Indisponível", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}}