{"name": "agendamento", "nodes": [{"parameters": {"httpMethod": "POST", "path": "processar", "responseMode": "responseNode", "options": {}}, "id": "52f1cb03-c2a6-4f65-8b38-a0a4c827d711", "name": "Webhook - <PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [360, -40], "webhookId": "processar-agendamento"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "validation-check", "leftValue": "={{ $json.body.nome }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "whatsapp-check", "leftValue": "={{ $json.body.whatsapp }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "data-check", "leftValue": "={{ $json.body.horario }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "4a3c475f-a800-4a0c-b267-5d7979ec1aaf", "leftValue": "={{ $json.body.imovel }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}, {"id": "616b5f0e-edc6-47af-b061-d5133d5f08a6", "leftValue": "={{ $json.body.data }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "2c599587-71c5-4fbe-a340-de09c2f6af12", "name": "Validação Dados", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, -40]}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "Visitas"}, "start": "={{ $json.body.dataHoraInicio }}", "end": "={{ $json.body.dataHoraFim }}", "additionalFields": {"description": "={{ $json.body.nome }}{{ $json.body.whatsapp }}{{ $json.body.imovel }}", "location": "={{ $json.body.imovel }}"}}, "id": "bbebbd1b-8797-4e5c-9c7b-e701f5da41cb", "name": "Criar Evento Calendar", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1, "position": [1460, -160], "credentials": {"googleCalendarOAuth2Api": {"id": "6BP3Rlb3TQDwnD6e", "name": "Google Calendar account"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\\n  \\\"status\\\": \\\"error\\\",\\n  \\\"message\\\": \\\"Dados obrigatórios não foram preenchidos\\\",\\n  \\\"required_fields\\\": [\\\"nome\\\", \\\"whatsapp\\\", \\\"data\\\", \\\"horario\\\", \\\"imovel\\\"]\\n}", "options": {"responseCode": 400}}, "id": "c45d4073-89e2-4a09-840f-0f50c5d1145c", "name": "Erro <PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [800, 100]}, {"parameters": {"resource": "messages-api", "instanceName": "instancia_teste", "remoteJid": "*************", "messageText": "=Visita agendada para:  {{ $('Validação Dados').item.json.body.imovel }} \nDia:{{ $('Validação Dados').item.json.body.data }} ás {{ $('Validação Dados').item.json.body.horario }}\n\nCliente:\n{{ $('Validação Dados').item.json.body.nome }}\nTelefone: \n{{ $('Validação Dados').item.json.body.whatsapp }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [1680, -60], "id": "50b1b94f-9f58-44de-8fa9-f28ef61d57a5", "name": "Mensagem Corretor", "credentials": {"evolutionApi": {"id": "MiMHoEJPAGhS31JQ", "name": "Evolution account"}}}, {"parameters": {"resource": "messages-api", "instanceName": "instancia_teste", "remoteJid": "={{ $('Validação Dados').item.json.body.whatsapp }}", "messageText": "=Visita para o imóvel: {{ $('Validação Dados').item.json.body.imovel }}\n\nConfirmada para: {{ $('Validação Dados').item.json.body.data }} ás {{ $('Validação Dados').item.json.body.horario }}", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [1680, -240], "id": "db2e5ecd-4f0e-4368-bae8-97f652fcc9a3", "name": "Mensagem Cliente", "credentials": {"evolutionApi": {"id": "MiMHoEJPAGhS31JQ", "name": "Evolution account"}}}, {"parameters": {"resource": "calendar", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "Visitas"}, "options": {}}, "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1.3, "position": [1260, -160], "id": "af9f6a87-638a-4293-ab71-289fc8476587", "name": "Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "6BP3Rlb3TQDwnD6e", "name": "Google Calendar account"}}}, {"parameters": {"resource": "calendar", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "Visitas"}, "timeMin": "={{ $json.body.dataHoraInicio }}", "timeMax": "={{ $json.body.dataHoraFim }}", "options": {}}, "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1.3, "position": [800, -140], "id": "fbc7d6d1-2041-4ea6-87c3-a51e208ef4fc", "name": "Google Calendar1", "credentials": {"googleCalendarOAuth2Api": {"id": "6BP3Rlb3TQDwnD6e", "name": "Google Calendar account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "db87eb8c-67db-46aa-8e84-20586371aba0", "leftValue": "={{ $json.available }}", "rightValue": "true", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1020, -140], "id": "b5956952-c599-49ad-8496-bc217579129a", "name": "If"}, {"parameters": {"jsCode": "// Gerar horários alternativos para o mesmo dia\nconst dataOriginal = $input.first().json.body.data;\nconst horariosDisponiveis = ['08:00', '09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00'];\n\n// Filtrar horário solicitado\nconst horarioSolicitado = $input.first().json.body.horario;\nconst horariosAlternativos = horariosDisponiveis.filter(h => h !== horarioSolicitado);\n\nconst horariosFormatados = horariosAlternativos.map(horario => ({\n  horario: horario,\n  dataHoraInicio: `${dataOriginal}T${horario}:00-03:00`,\n  dataHoraFim: `${dataOriginal}T${String(parseInt(horario.split(':')[0]) + 1).padStart(2, '0')}:00:00-03:00`\n}));\n\nreturn {\n  json: {\n    status: \"indisponivel\",\n    message: \"Horário não disponível. Veja os horários alternativos para o mesmo dia:\",\n    dataOriginal: dataOriginal,\n    horarioSolicitado: horarioSolicitado,\n    horariosAlternativos: horariosFormatados\n  }\n};"}, "id": "gerar-alternativas-main", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1240, 60]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json) }}", "options": {"responseCode": 409}}, "id": "resposta-indisponivel-main", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1460, 60]}], "pinData": {}, "connections": {"Webhook - Processar Dados": {"main": [[{"node": "Validação Dados", "type": "main", "index": 0}]]}, "Validação Dados": {"main": [[{"node": "Google Calendar1", "type": "main", "index": 0}], [{"node": "Erro <PERSON>", "type": "main", "index": 0}]]}, "Criar Evento Calendar": {"main": [[{"node": "Mensagem Cliente", "type": "main", "index": 0}, {"node": "Mensagem Corretor", "type": "main", "index": 0}]]}, "Google Calendar": {"main": [[{"node": "Criar Evento Calendar", "type": "main", "index": 0}]]}, "Google Calendar1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Google Calendar", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "8142deb5-a4b3-4183-b55f-7af7f06ce1d8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "82be3653791add1543a038c18cc4abc1a13fd8c4ea5b94950a13a7e0cef38127"}, "id": "L0UtRtDlV0L8CsLP", "tags": [{"createdAt": "2025-05-27T19:31:37.636Z", "updatedAt": "2025-05-27T19:31:37.636Z", "id": "8RyG3XT48d3pD5Uy", "name": "Imobiliária"}, {"createdAt": "2025-05-27T19:42:44.377Z", "updatedAt": "2025-05-27T19:42:44.377Z", "id": "eahcL9Cfl7d2Sbe4", "name": "Imobiliária Completo"}]}